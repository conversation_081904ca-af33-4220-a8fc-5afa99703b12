# git_hooks设计说明
## 方案选择

### 所有团队统一git钩子，在钩子内进行判断

详细方案：
1. 在repo仓库的hooks文件夹中，修改原有的commit-msg钩子，添加团队判断逻辑
2. 根据团队不同编写不同的钩子处理逻辑，使用单独仓库存放
3. commit-msg钩子被执行时，根据当前仓库所属团队执行对应的钩子处理逻辑

这种方案需要在钩子内部根据项目或团队进行判断，实现复杂，维护困难，不利于团队自定义规则。

### 修改repo源码，增加git-hooks的处理逻辑

详细方案：
1. 在repo源码中增加git-hooks的处理逻辑，支持在manifest中配置git-hooks元素，为各个项目指定对应的git hooks仓库和分支
2. repo运行时根据manifest配置自动为各个项目安装对应的git hooks

这种方案需要修改repo源码，增加专门的git-hooks处理逻辑，实现复杂。

### 修改repo源码，调整repo-hooks逻辑，加入post-sync钩子

详细方案：
1. 在repo源码中调整repo-hooks的逻辑，增加post-sync钩子
2. 在repo-hooks项目中实现post-sync.py脚本，解析manifest配置，为各个项目安装对应的git hooks

利用repo已有的hooks机制，添加post-sync钩子，在repo sync完成后自动为各个项目安装对应团队的git hooks，对repo代码的修改量最小。

### 方案比较
| 方案 | 优点 | 缺点 |
| --- | --- | --- |
| 统一钩子 | 实现简单，统一管理 | 不灵活，难以满足不同团队需求 |
| 修改repo源码 | 可定制性强 | 维护成本高，每次repo更新需要重新适配 |
| 利用repo-hooks | 实现简单，利用现有机制，易于维护 | 需要在manifest中配置repo-hooks项目 |

最终选择了第三种方案，通过post-sync钩子实现团队特定git hooks的自动安装。

## 项目结构
```
git_hooks/
├── post-sync.py         # repo post-sync钩子主脚本
├── targets/             # 项目与团队映射配置目录
│   ├── 仓库名1/
│   │   └── target       # 包含团队名称的文件
│   ├── 仓库名2/
│   │   └── target
│   └── ...
└── teams/               # 团队特定钩子目录
    ├── 团队名1/
    │   ├── commit-msg   # 提交消息钩子
    │   └── ...          # 其他钩子
    ├── 团队名2/
    │   └── ...
    └── ...
```

## 生效逻辑
1. 当执行`repo sync`命令时，repo会在同步完成后自动调用post-sync钩子
2. post-sync.py脚本会解析manifest文件，获取所有项目信息
3. 对于每个项目，脚本会在targets目录下查找对应的target文件，获取团队名称
4. 根据团队名称，将teams目录下对应团队的钩子文件复制到项目的.git/hooks目录
5. 设置钩子文件的执行权限，确保钩子可以正常运行

## 钩子维护
### 在manifest中启用
在项目的manifest.xml中添加repo-hooks配置：
```xml
<manifest>
  <remote name="origin" fetch="..." />

  <!-- 配置repo-hooks项目并启用post-sync钩子 -->
  <project name="Router/git_hooks" path="git_hooks" revision="master"/>
  <repo-hooks in-project="Router/git_hooks" enabled-list="post-sync" />

  <!-- 其他项目配置 -->
  <project name="Router/Iplatform" path="Iplatform" revision="re235be_dev_20240204"/>
  <project name="Deco/SDK/QCA/qca_95xx_12_2" path="qca_95xx_12_2" revision="re/12.2csu3"/>
  
</manifest>
```

### 增加需要安装钩子的仓库
1. 在targets目录下创建与项目名称对应的目录，例如`Router/Iplatform`
2. 在该目录下创建target文件，内容为团队名称
3. 如果不需要安装钩子，可以将target文件内容设置为"-"，表示跳过此项目
    - 如果没有显式设置则会出现警告

例如：
```
mkdir -p targets/Router/Iplatform
echo "团队名" > targets/Router/Iplatform/target
```

### 增加团队
1. 在teams目录下创建与团队名称对应的目录
2. 在该目录下添加需要的git钩子文件（如commit-msg, prepare-commit-msg等）
3. 确保钩子文件有执行权限

例如：
```
mkdir -p teams/新团队名
cp 钩子文件 teams/新团队名/
chmod +x teams/新团队名/*
```

## 使用方式
1. 确保manifest中已正确配置repo-hooks
2. 执行`repo sync`命令，系统会自动安装对应的git钩子
3. 钩子安装成功后，在对应项目中进行git操作时会自动触发相应的钩子

对于不使用repo管理的项目，可以手动将对应的钩子文件复制到.git/hooks目录，并设置执行权限

## 团队规则介绍

### Archer的commit message规范
Archer团队的commit message需遵循以下格式：

1. **Head格式**：`[模块名] 类型 #ID: 描述`
   - 必须以`[模块名]`开头，后跟空格
   - 类型可以是Fix等
   - 长度不超过65字节

2. **Body格式**：
   - Head与Body之间必须有空行
   - 每行长度不超过70字节
   - Fix类型的提交必须包含以下标签：
     - [问题描述]：具体描述问题现象
     - [问题原因]：说明问题根源
     - [解决方案]：描述修复方案
     - [自测内容]：列出测试要点

3. **自动处理**：
   - 自动添加Change-Id（用于代码审查）
   - 自动添加Signed-off-by信息
   - 提供commit message模板
