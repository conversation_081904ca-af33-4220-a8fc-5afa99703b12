#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import shutil
import xml.etree.ElementTree as ET
import subprocess
import logging

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='/tmp/post-sync{}.log'.format(os.getpid()),
    filemode='w'
)
logger = logging.getLogger('post-sync')

def find_repo_root():
    """Find the .repo directory by walking up the directory tree."""
    path = os.getcwd()
    while path != '/':
        if os.path.exists(os.path.join(path, '.repo')):
            return path
        path = os.path.dirname(path)
    return None

def get_current_manifests():
    """Get information about all currently used manifests."""
    repo_root = find_repo_root()
    if not repo_root:
        return None
    
    repo_dir = os.path.join(repo_root, '.repo')
    manifests = {
        'main': [],
        'local': [],
        'sub': []
    }
    
    # 获取主manifest
    main_manifest = os.path.join(repo_dir, 'manifest.xml')
    if os.path.exists(main_manifest):
        manifests['main'].append(os.path.realpath(main_manifest))
    
    # 获取本地manifests
    local_manifests_dir = os.path.join(repo_dir, 'local_manifests')
    if os.path.exists(local_manifests_dir):
        manifests['local'] = [
            os.path.join(local_manifests_dir, f)
            for f in os.listdir(local_manifests_dir)
            if f.endswith('.xml')
        ]
    
    # 获取submanifests
    submanifests_dir = os.path.join(repo_dir, 'submanifests')
    if os.path.exists(submanifests_dir):
        for subdir in os.listdir(submanifests_dir):
            manifest_path = os.path.join(submanifests_dir, subdir, 'manifest.xml')
            if os.path.exists(manifest_path):
                manifests['sub'].append(manifest_path)
    
    return manifests

def print_manifests():
    """Print information about all currently used manifests."""
    manifests = get_current_manifests()
    if not manifests:
        print("Not in a repo workspace")
        return
    
    print("Current Manifests:")
    print(f"Main manifest: {manifests['main']}")
    
    if manifests['local']:
        print("\nLocal manifests:")
        for m in manifests['local']:
            print(f"  {m}")
    
    if manifests['sub']:
        print("\nSubmanifests:")
        for m in manifests['sub']:
            print(f"  {m}")

def get_project_name():
    """获取当前项目名称"""
    try:
        # 获取当前工作目录
        cwd = os.getcwd()
        logger.info(f"当前工作目录: {cwd}")

        # 使用repo list命令获取当前项目信息
        result = subprocess.run(
            ['repo', 'list', '--path-only', cwd],
            capture_output=True, text=True, check=True
        )
        project_name = result.stdout.strip()
        logger.info(f"获取到项目名称: {project_name}")
        return project_name
    except subprocess.CalledProcessError as e:
        logger.error(f"获取项目名称失败: {e}")
        return None

def parse_manifest(manifests):
    """解析manifest文件，获取所有project的path属性，返回嵌套数组"""
    project_lookup_table = {}
    try:
        logger.info(f"获取到的manifests: {manifests}")
        if not manifests:
            logger.error("未找到manifest文件")
            return None
        for manifest_type, manifest_list in manifests.items():
            for manifest_path in manifest_list:
                tree = ET.parse(manifest_path)
                for includes in tree.findall('include'):
                    name = includes.get('name')
                    main_manifest_dir = os.path.join(os.path.dirname(manifest_path), "manifests")
                    main_manifest_path = os.path.join(main_manifest_dir, name)
                    if os.path.exists(main_manifest_path):
                        logger.info(f"解析include: {main_manifest_path}")
                        project_lookup_table.update(parse_manifest({manifest_type: [main_manifest_path]}))
                    else:
                        logger.warning(f"include文件不存在: {main_manifest_path}")
                for project in tree.findall('project'):
                    name = project.get('name')
                    path = project.get('path')
                    if name and path:
                        if name not in project_lookup_table:
                            project_lookup_table[name] = []
                        project_lookup_table[name].append(path)
        return project_lookup_table
    except Exception as e:
        logger.error(f"解析manifest失败: {e}")
        return None
    

def find_team_name(project_name):
    """在targets目录下查找target文件，获取团队名称"""
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        targets_dir = os.path.join(script_dir, 'targets')
        search_dir = os.path.join(targets_dir, project_name)
        if os.path.exists(search_dir):
            # 尝试读取此目录的target文件
            target_file = os.path.join(search_dir, 'target')
            if os.path.exists(target_file):
                with open(target_file, 'r') as f:
                    team_name = f.read().strip()
                    logger.info(f"获取到团队名称: {team_name}")
                    return team_name
            else:
                logger.warning(f"target文件不存在: {target_file}")
        return None
    except Exception as e:
        logger.error(f"查找团队名称失败: {e}")
        return None

def copy_hooks(team_name, project_loc):
    """复制团队对应的hooks到当前git仓库"""
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 团队hooks目录
        team_hooks_dir = os.path.join(script_dir, 'teams', team_name)
        if not os.path.exists(team_hooks_dir):
            logger.error(f"团队hooks目录不存在: {team_hooks_dir}")
            return False

        # 当前git仓库的hooks目录
        git_dir = os.path.join(project_loc, '.git')
        logger.info(f"git_dir: {git_dir}")
        # git_dir可能是文件，也可能是目录
        if not os.path.exists(git_dir):
            logger.error(f"当前目录不是git仓库: {project_loc}")
            return False
        
        # 判断是否为文件
        if os.path.isfile(git_dir):
            # 读取gitdir
            # 格式： gitdir: {path}
            with open(git_dir, 'r') as f:
                content = f.read().strip()
                if content.startswith('gitdir:'):
                    git_dir = os.path.join(project_loc, content.split('gitdir:')[1].strip())
                else:
                    logger.error(f"gitdir格式错误: {content}")
                    return False
                

        git_hooks_dir = os.path.join(git_dir, 'hooks')
        if not os.path.exists(git_hooks_dir):
            os.makedirs(git_hooks_dir)

        # 复制hooks文件
        hooks_copied = False
        for hook_file in os.listdir(team_hooks_dir):
            source = os.path.join(team_hooks_dir, hook_file)
            destination = os.path.join(git_hooks_dir, hook_file)

            if os.path.isfile(source):
                shutil.copy2(source, destination)
                # 确保hook文件有执行权限
                os.chmod(destination, 0o755)
                logger.info(f"复制hook: {source} -> {destination}")
                hooks_copied = True

        return hooks_copied
    except Exception as e:
        logger.error(f"复制hooks失败: {e}")
        return False

def process_project(project_name, project_loc_list):
    """处理单个项目"""
    try:
        logger.info(f"处理项目: {project_name}")

        # 查找团队名称
        team_name = find_team_name(project_name)
        if not team_name:
            logger.warning(f"无法获取项目 {project_name} 的团队名称，跳过")
            return False
        
        # 如果团队名称为"-"则跳过
        if team_name == "-":
            logger.info(f"团队名称为'-'，不需要处理hooks")
            return True

        # 复制hooks
        for project_loc in project_loc_list:
            project_path = os.path.join(os.getcwd(), project_loc)
            if copy_hooks(team_name, project_path):
                logger.info(f"成功复制 {team_name} 团队的hooks到路径 {project_path}")
                print(f"成功复制 {team_name} 团队的hooks到路径 {project_path}")
                return True
            else:
                logger.warning(f"路径 {project_path} 没有hooks被复制")
                return False
    except Exception as e:
        logger.error(f"处理项目 {project_name} 时发生错误: {e}")
        return False

def main(project_list=None, **kwargs):
    """Main function invoked directly by repo after sync completes.

    We must use the name "main" as that is what repo requires.

    Args:
        project_list: List of projects that were synced. If None, all projects
            in the manifest were synced.
        kwargs: Leave this here for forward-compatibility.
    """
    try:
        logger.info("开始执行post-sync.py")
        logger.info(f"接收到的项目列表: {project_list}")

        # 解析manifest文件
        project_lookup_table = parse_manifest(get_current_manifests())

        if not project_list:
            logger.info("未提供项目列表，尝试获取当前项目")
            project_list = project_lookup_table.keys()
            logger.info(f"获取到的项目列表: {project_list}")

        success_count = 0
        for project_name in project_list:
            if process_project(project_name, project_lookup_table[project_name]):
                success_count += 1

        logger.info(f"处理完成: {success_count}/{len(project_list)} 个项目成功")
        # 输出log至控制台
        if success_count == len(project_list):
            print("git-hooks复制成功")
        else:
            with open('/tmp/post-sync{}.log'.format(os.getpid()), 'r') as f:
                print(f.read())

        return 0
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        with open('/tmp/post-sync{}.log'.format(os.getpid()), 'r') as f:
            print(f.read())
        return 1

if __name__ == "__main__":
    sys.exit(main())
